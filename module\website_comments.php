<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

/**
 * 网站评论功能模块
 * 处理评论提交、获取评论列表、域名过滤等功能
 */

// 确保用户模块已加载
if (!function_exists('check_user_login')) {
    require_once(ROOT_PATH . 'source/module/user.php');
}

/**
 * 获取网站评论列表
 * @param int $web_id 网站ID
 * @param int $limit 限制数量
 * @return array 评论列表
 */
function get_website_comments($web_id, $limit = 10) {
    global $DB;
    
    $web_id = intval($web_id);
    $limit = intval($limit);
    
    $sql = "SELECT c.*, u.nick_name as member_name, u.user_email as member_email 
            FROM " . $DB->table('website_comments') . " c 
            LEFT JOIN " . $DB->table('users') . " u ON c.user_id = u.user_id 
            WHERE c.web_id = $web_id AND c.status = 1 AND c.parent_id = 0 
            ORDER BY c.create_time DESC 
            LIMIT $limit";
    
    $query = $DB->query($sql);
    $comments = array();
    
    while ($row = $DB->fetch_array($query)) {
        // 处理用户信息
        if ($row['user_id'] > 0) {
            // 会员评论
            $row['display_name'] = $row['member_name'] ? $row['member_name'] : '会员用户';
            $row['display_email'] = $row['member_email'];
            $row['is_member'] = true;
        } else {
            // 匿名评论
            $row['display_name'] = '匿名';
            $row['display_email'] = '';
            $row['is_member'] = false;
        }
        
        // 格式化IP地址显示
        $row['display_ip'] = mask_ip($row['user_ip']);
        
        // 格式化时间
        $row['display_time'] = date('Y-m-d H:i:s', $row['create_time']);
        
        // 获取回复
        $row['replies'] = get_comment_replies($row['comment_id']);
        
        $comments[] = $row;
    }
    
    $DB->free_result($query);
    return $comments;
}

/**
 * 获取评论回复
 * @param int $parent_id 父评论ID
 * @return array 回复列表
 */
function get_comment_replies($parent_id) {
    global $DB;
    
    $parent_id = intval($parent_id);
    
    $sql = "SELECT c.*, u.nick_name as member_name, u.user_email as member_email 
            FROM " . $DB->table('website_comments') . " c 
            LEFT JOIN " . $DB->table('users') . " u ON c.user_id = u.user_id 
            WHERE c.parent_id = $parent_id AND c.status = 1 
            ORDER BY c.create_time ASC";
    
    $query = $DB->query($sql);
    $replies = array();
    
    while ($row = $DB->fetch_array($query)) {
        // 处理用户信息
        if ($row['user_id'] > 0) {
            $row['display_name'] = $row['member_name'] ? $row['member_name'] : '会员用户';
            $row['display_email'] = $row['member_email'];
            $row['is_member'] = true;
        } else {
            $row['display_name'] = '匿名';
            $row['display_email'] = '';
            $row['is_member'] = false;
        }
        
        $row['display_ip'] = mask_ip($row['user_ip']);
        $row['display_time'] = date('Y-m-d H:i:s', $row['create_time']);
        
        $replies[] = $row;
    }
    
    $DB->free_result($query);
    return $replies;
}

/**
 * 提交网站评论
 * @param array $data 评论数据
 * @return array 结果信息
 */
function submit_website_comment($data) {
    global $DB;
    
    // 验证必要字段
    if (empty($data['web_id']) || empty($data['comment_content'])) {
        return array('success' => false, 'message' => '请填写完整的评论信息');
    }
    
    // 过滤域名
    $filtered_content = filter_domain_names($data['comment_content']);
    if ($filtered_content !== $data['comment_content']) {
        return array('success' => false, 'message' => '评论内容不能包含域名链接');
    }
    
    // 获取用户IP
    $user_ip = get_client_ip();
    
    // 检查IP是否在短时间内重复提交
    $recent_time = time() - 300; // 5分钟内
    $check_sql = "SELECT comment_id FROM " . $DB->table('website_comments') . " 
                  WHERE user_ip = '$user_ip' AND create_time > $recent_time";
    $check_query = $DB->query($check_sql);
    if ($DB->num_rows($check_query) > 0) {
        return array('success' => false, 'message' => '您提交评论过于频繁，请稍后再试');
    }
    
    // 准备插入数据
    $insert_data = array(
        'web_id' => intval($data['web_id']),
        'user_id' => isset($data['user_id']) ? intval($data['user_id']) : 0,
        'user_email' => isset($data['user_email']) ? trim($data['user_email']) : '',
        'user_name' => isset($data['user_name']) ? trim($data['user_name']) : '',
        'user_ip' => $user_ip,
        'content_quality' => intval($data['content_quality']),
        'service_quality' => intval($data['service_quality']),
        'trust_level' => intval($data['trust_level']),
        'comment_content' => trim($filtered_content),
        'parent_id' => isset($data['parent_id']) ? intval($data['parent_id']) : 0,
        'status' => 1,
        'create_time' => time()
    );
    
    // 插入数据库
    $result = $DB->insert($DB->table('website_comments'), $insert_data);
    
    if ($result) {
        return array('success' => true, 'message' => '评论提交成功');
    } else {
        return array('success' => false, 'message' => '评论提交失败，请重试');
    }
}

/**
 * 过滤域名
 * @param string $content 内容
 * @return string 过滤后的内容
 */
function filter_domain_names($content) {
    // 常见域名后缀
    $domain_suffixes = array(
        'com', 'cn', 'net', 'org', 'gov', 'edu', 'mil', 'int', 'info', 'biz', 
        'name', 'pro', 'museum', 'coop', 'aero', 'xxx', 'idv', 'mobi', 'tel',
        'asia', 'jobs', 'travel', 'cat', 'post', 'cc', 'tv', 'tk', 'me', 'co',
        'io', 'ai', 'app', 'dev', 'tech', 'online', 'site', 'website', 'store',
        'shop', 'blog', 'news', 'top', 'xyz', 'club', 'vip', 'wang', 'ren',
        'xin', 'link', 'click', 'fun', 'game', 'help', 'live', 'work', 'world'
    );
    
    // 构建正则表达式
    $pattern = '/\b[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.(' . implode('|', $domain_suffixes) . ')\b/i';
    
    // 替换域名为***
    $filtered = preg_replace($pattern, '***', $content);
    
    return $filtered;
}

/**
 * 掩码IP地址
 * @param string $ip IP地址
 * @return string 掩码后的IP
 */
function mask_ip($ip) {
    if (strpos($ip, '.') !== false) {
        // IPv4
        $parts = explode('.', $ip);
        if (count($parts) == 4) {
            return $parts[0] . '.' . $parts[1] . '.' . $parts[2] . '.*';
        }
    } elseif (strpos($ip, ':') !== false) {
        // IPv6
        $parts = explode(':', $ip);
        if (count($parts) >= 4) {
            return $parts[0] . ':' . $parts[1] . ':' . $parts[2] . ':*';
        }
    }
    
    return $ip;
}

/**
 * 获取网站评论统计
 * @param int $web_id 网站ID
 * @return array 统计信息
 */
function get_website_comment_stats($web_id) {
    global $DB;
    
    $web_id = intval($web_id);
    
    $sql = "SELECT 
                COUNT(*) as total_comments,
                AVG(content_quality) as avg_content_quality,
                AVG(service_quality) as avg_service_quality,
                AVG(trust_level) as avg_trust_level
            FROM " . $DB->table('website_comments') . " 
            WHERE web_id = $web_id AND status = 1 AND parent_id = 0";
    
    $result = $DB->fetch_one($sql);
    
    if ($result) {
        $result['avg_content_quality'] = round($result['avg_content_quality'], 1);
        $result['avg_service_quality'] = round($result['avg_service_quality'], 1);
        $result['avg_trust_level'] = round($result['avg_trust_level'], 1);
        $result['avg_overall'] = round(($result['avg_content_quality'] + $result['avg_service_quality'] + $result['avg_trust_level']) / 3, 1);
    } else {
        $result = array(
            'total_comments' => 0,
            'avg_content_quality' => 0,
            'avg_service_quality' => 0,
            'avg_trust_level' => 0,
            'avg_overall' => 0
        );
    }
    
    return $result;
}
?>
