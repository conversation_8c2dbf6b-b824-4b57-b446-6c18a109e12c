<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$pagename = '黑名单';
$pageurl = '?mod=blacklist';
$tempfile = 'blacklist.html';

$pagesize = 20;
$curpage = intval(isset($_GET['page']) ? $_GET['page'] : 1);
if ($curpage > 1) {
    $start = ($curpage - 1) * $pagesize;
} else {
    $start = 0;
    $curpage = 1;
}

// 分页SEO优化 - 在标题中添加页码信息
$seo_title_suffix = '';
if ($curpage > 1) {
    $seo_title_suffix = '(第' . $curpage . '页)';
}

$category = intval(isset($_GET['category']) ? $_GET['category'] : 0);
$cache_id = $category . '-' . $curpage;

if ($category > 0) {
    $pageurl .= '&category=' . $category;
}

// 获取黑名单分类（与详情页保持一致）
function get_blacklist_categories() {
    $categories = array();
    $categories[0] = '其他';
    $categories[1] = '违法违规';
    $categories[2] = '色情内容';
    $categories[3] = '赌博博彩';
    $categories[4] = '诈骗欺诈';
    $categories[5] = '恶意软件';
    $categories[6] = '垃圾信息';
    $categories[7] = '版权侵权';
    $categories[8] = '政治敏感';

    return $categories;
}

if (!$smarty->isCached($tempfile, $cache_id)) {
    // 构建查询条件 - 参照VIP页面模式
    $where = "w.web_status=1";
    if ($category > 0) {
        // 检查是否有黑名单分类字段
        $table_name = $DB->table('websites');
        $check_sql = "SHOW COLUMNS FROM `{$table_name}` LIKE 'web_blacklist_category'";
        $check_result = $DB->query($check_sql);
        $has_category_field = $DB->num_rows($check_result) > 0;

        if ($has_category_field) {
            $where .= " AND w.web_blacklist_category=$category";
        }
    }

    // 获取黑名单网站列表 - 按最后添加时间排序
    $sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_intro, w.web_tags, w.web_ctime, w.cate_id, w.web_pic,
                   w.web_blacklist_reason, w.web_blacklist_category, w.web_blacklist_time, w.web_blacklist_operator,
                   c.cate_name
            FROM " . $DB->table('websites') . " w
            LEFT JOIN " . $DB->table('categories') . " c ON w.cate_id = c.cate_id
            WHERE $where
            ORDER BY COALESCE(w.web_blacklist_time, w.web_ctime) DESC
            LIMIT $start, $pagesize";

    $websites = $DB->fetch_all($sql);

    // 格式化数据 - 参照VIP页面处理方式
    foreach ($websites as &$web) {
        // 确保黑名单字段有默认值
        if (!isset($web['web_blacklist_category']) || $web['web_blacklist_category'] === null) {
            $web['web_blacklist_category'] = 0;
        }
        if (empty($web['web_blacklist_reason'])) {
            $web['web_blacklist_reason'] = '该网站已被列入黑名单';
        }
        if (empty($web['web_blacklist_time'])) {
            $web['web_blacklist_time'] = $web['web_ctime'];
        }
        if (empty($web['web_blacklist_operator'])) {
            $web['web_blacklist_operator'] = 'system';
        }

        $web['web_furl'] = format_url($web['web_url']);
        $web['web_pic'] = get_webthumb($web['web_pic']);
        $web['web_ctime'] = date('Y-m-d', $web['web_ctime']);
        $web['web_link'] = '?mod=blacklist_detail&id=' . $web['web_id'];
        $web['cate_link'] = '?mod=blacklist&category=' . $web['web_blacklist_category'];

        // 处理分类名称
        $categories = get_blacklist_categories();
        $category_id = intval($web['web_blacklist_category']);
        $web['category_name'] = isset($categories[$category_id]) ? $categories[$category_id] : $categories[0];

        // 格式化时间和原因
        $web['blacklist_time_formatted'] = $web['web_blacklist_time'] ? date('Y-m-d', $web['web_blacklist_time']) : date('Y-m-d', $web['web_ctime']);
        $web['reason_short'] = mb_strlen($web['web_blacklist_reason']) > 100 ?
                               mb_substr($web['web_blacklist_reason'], 0, 100) . '...' :
                               $web['web_blacklist_reason'];

        // 判断是否为今天添加到黑名单的网站
        $blacklist_time = $web['web_blacklist_time'] ? $web['web_blacklist_time'] : $web['web_ctime'];
        $web['is_today'] = (date('Y-m-d', $blacklist_time) == date('Y-m-d'));

        // 隐藏网站URL，只显示域名
        $url_to_parse = $web['web_url'];
        if (!preg_match('/^https?:\/\//', $url_to_parse)) {
            $url_to_parse = 'http://' . $url_to_parse;
        }
        $web['domain'] = parse_url($url_to_parse, PHP_URL_HOST);
        if (!$web['domain']) {
            $web['domain'] = $web['web_url'];
        }
    }

    // 获取总数 - 参照VIP页面方式
    $total = $DB->get_count($DB->table('websites') . ' w', $where);

    // 分页
    $showpage = showpage($pageurl, $total, $curpage, $pagesize);

    // 获取分类列表
    $categories = get_blacklist_categories();

    // 获取侧边栏数据 - VIP网站和推荐文章
    $vip_websites = array();
    $recommended_articles = array();

    // 尝试获取VIP网站数据
    try {
        if (function_exists('get_websites')) {
            $vip_websites = get_websites(0, 6, false, true);
        }
    } catch (Exception $e) {
        // 如果获取失败，使用空数组
        $vip_websites = array();
    }

    // 尝试获取推荐文章数据
    try {
        if (function_exists('get_articles')) {
            $recommended_articles = get_articles(0, 8);
        }
    } catch (Exception $e) {
        // 如果获取失败，使用空数组
        $recommended_articles = array();
    }

    // 优化黑名单页面SEO
    $categories = get_blacklist_categories();

    if ($category > 0 && isset($categories[$category])) {
        // 分类黑名单页面SEO
        $category_name = $categories[$category];
        $seo_title = $category_name . '黑名单' . $seo_title_suffix . ' - 违规网站举报 - ' . $options['site_name'];

        $seo_keywords = array($category_name . '黑名单', $category_name . '违规网站', '网站举报', '违规举报', '黑名单查询');
        if (!empty($options['site_keywords'])) {
            $site_keywords = explode(',', $options['site_keywords']);
            $seo_keywords = array_merge($seo_keywords, $site_keywords);
        }

        $seo_description = $category_name . '黑名单，收录违规的' . $category_name . '网站';
        if ($total > 0) {
            $seo_description .= '，共' . $total . '个违规网站';
        }
        $seo_description .= '。' . $options['site_name'] . '为您提供网站违规举报和黑名单查询服务。';
    } else {
        // 全部黑名单页面SEO
        $seo_title = '网站黑名单' . $seo_title_suffix . ' - 违规网站举报查询 - ' . $options['site_name'];

        $seo_keywords = array('网站黑名单', '违规网站', '不良网站', '网站举报', '违规举报', '黑名单查询', '网站安全');
        if (!empty($options['site_keywords'])) {
            $site_keywords = explode(',', $options['site_keywords']);
            $seo_keywords = array_merge($seo_keywords, $site_keywords);
        }

        $seo_description = '网站黑名单大全，收录各类违规和不良网站';
        if ($total > 0) {
            $seo_description .= '，共' . $total . '个违规网站';
        }
        $seo_description .= '。' . $options['site_name'] . '为您提供网站违规举报和黑名单查询服务，维护网络安全。';
    }

    // 确保描述长度适中
    if (mb_strlen($seo_description, 'UTF-8') > 160) {
        $seo_description = mb_substr($seo_description, 0, 157, 'UTF-8') . '...';
    }

    // 设置模板变量
    $smarty->assign('site_title', $seo_title);
    $smarty->assign('site_keywords', implode(',', array_unique($seo_keywords)));
    $smarty->assign('site_description', $seo_description);
    $smarty->assign('site_path', get_sitepath() . ' &raquo; ' . $pagename);
    $smarty->assign('site_rss', get_rssfeed());

    $smarty->assign('websites', $websites);
    $smarty->assign('categories', $categories);
    $smarty->assign('current_category', $category);
    $smarty->assign('showpage', $showpage);
    $smarty->assign('total', $total);
    $smarty->assign('pagename', $pagename);

    // 分页SEO优化 - 计算分页信息
    $total_pages = ceil($total / $pagesize);
    $prev_page = ($curpage > 1) ? $curpage - 1 : 0;
    $next_page = ($curpage < $total_pages) ? $curpage + 1 : 0;

    // 生成分页相关的URL
    $canonical_url = $options['site_url'] . '?mod=blacklist';
    if ($category > 0) {
        $canonical_url .= '&category=' . $category;
    }
    if ($curpage > 1) {
        $canonical_url .= '&page=' . $curpage;
    }

    $prev_url = '';
    $next_url = '';
    if ($prev_page > 0) {
        $prev_url = $options['site_url'] . '?mod=blacklist';
        if ($category > 0) {
            $prev_url .= '&category=' . $category;
        }
        if ($prev_page > 1) {
            $prev_url .= '&page=' . $prev_page;
        }
    }
    if ($next_page > 0) {
        $next_url = $options['site_url'] . '?mod=blacklist';
        if ($category > 0) {
            $next_url .= '&category=' . $category;
        }
        $next_url .= '&page=' . $next_page;
    }

    // 生成robots meta标签 - 第一页允许索引，其他页面noindex但follow
    $robots_content = ($curpage == 1) ? 'index,follow' : 'noindex,follow';

    // 分页SEO相关变量
    $smarty->assign('curpage', $curpage);
    $smarty->assign('total_pages', $total_pages);
    $smarty->assign('canonical_url', $canonical_url);
    $smarty->assign('prev_url', $prev_url);
    $smarty->assign('next_url', $next_url);
    $smarty->assign('robots_content', $robots_content);

    // 侧边栏数据
    $smarty->assign('vip_websites', $vip_websites);
    $smarty->assign('recommended_articles', $recommended_articles);
}

smarty_output($tempfile, $cache_id);
?>