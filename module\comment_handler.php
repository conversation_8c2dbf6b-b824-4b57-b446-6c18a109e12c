<?php
/**
 * 评论处理器 - 处理AJAX评论请求
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 引入必要文件
require_once('../source/init.php');
require_once('../source/module/user.php');
require_once('./website_comments.php');

// 获取操作类型
$action = isset($_POST['action']) ? trim($_POST['action']) : '';

switch ($action) {
    case 'submit_comment':
        handle_submit_comment();
        break;
    case 'get_comments':
        handle_get_comments();
        break;
    case 'submit_reply':
        handle_submit_reply();
        break;
    default:
        output_json(array('success' => false, 'message' => '无效的操作'));
        break;
}

/**
 * 处理提交评论
 */
function handle_submit_comment() {
    global $DB;
    
    // 获取表单数据
    $web_id = intval($_POST['web_id']);
    $content_quality = intval($_POST['content_quality']);
    $service_quality = intval($_POST['service_quality']);
    $trust_level = intval($_POST['trust_level']);
    $comment_content = trim($_POST['comment_content']);
    
    // 验证数据
    if (empty($web_id) || empty($comment_content)) {
        output_json(array('success' => false, 'message' => '请填写完整的评论信息'));
        return;
    }
    
    if ($content_quality < 1 || $content_quality > 5 || 
        $service_quality < 1 || $service_quality > 5 || 
        $trust_level < 1 || $trust_level > 5) {
        output_json(array('success' => false, 'message' => '评分必须在1-5星之间'));
        return;
    }
    
    if (mb_strlen($comment_content, 'utf-8') < 10) {
        output_json(array('success' => false, 'message' => '评论内容至少需要10个字符'));
        return;
    }
    
    if (mb_strlen($comment_content, 'utf-8') > 500) {
        output_json(array('success' => false, 'message' => '评论内容不能超过500个字符'));
        return;
    }
    
    // 检查用户登录状态
    $auth_cookie = isset($_COOKIE['auth_cookie']) ? $_COOKIE['auth_cookie'] : '';
    $user_info = check_user_login($auth_cookie);
    
    // 准备评论数据
    $comment_data = array(
        'web_id' => $web_id,
        'content_quality' => $content_quality,
        'service_quality' => $service_quality,
        'trust_level' => $trust_level,
        'comment_content' => $comment_content
    );
    
    if (!empty($user_info)) {
        // 会员评论
        $comment_data['user_id'] = $user_info['user_id'];
        $comment_data['user_email'] = $user_info['user_email'];
        $comment_data['user_name'] = $user_info['nick_name'];
    } else {
        // 匿名评论
        $comment_data['user_id'] = 0;
        $comment_data['user_email'] = '';
        $comment_data['user_name'] = '匿名';
    }
    
    // 提交评论
    $result = submit_website_comment($comment_data);
    output_json($result);
}

/**
 * 处理获取评论列表
 */
function handle_get_comments() {
    $web_id = intval($_POST['web_id']);
    $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 10;
    
    if (empty($web_id)) {
        output_json(array('success' => false, 'message' => '网站ID不能为空'));
        return;
    }
    
    $comments = get_website_comments($web_id, $limit);
    $stats = get_website_comment_stats($web_id);
    
    output_json(array(
        'success' => true,
        'comments' => $comments,
        'stats' => $stats
    ));
}

/**
 * 处理提交回复
 */
function handle_submit_reply() {
    global $DB;
    
    // 获取表单数据
    $web_id = intval($_POST['web_id']);
    $parent_id = intval($_POST['parent_id']);
    $comment_content = trim($_POST['comment_content']);
    
    // 验证数据
    if (empty($web_id) || empty($parent_id) || empty($comment_content)) {
        output_json(array('success' => false, 'message' => '请填写完整的回复信息'));
        return;
    }
    
    if (mb_strlen($comment_content, 'utf-8') < 5) {
        output_json(array('success' => false, 'message' => '回复内容至少需要5个字符'));
        return;
    }
    
    if (mb_strlen($comment_content, 'utf-8') > 300) {
        output_json(array('success' => false, 'message' => '回复内容不能超过300个字符'));
        return;
    }
    
    // 检查父评论是否存在
    $parent_comment = $DB->fetch_one("SELECT comment_id FROM " . $DB->table('website_comments') . " WHERE comment_id = $parent_id AND web_id = $web_id AND status = 1");
    if (!$parent_comment) {
        output_json(array('success' => false, 'message' => '要回复的评论不存在'));
        return;
    }
    
    // 检查用户登录状态
    $auth_cookie = isset($_COOKIE['auth_cookie']) ? $_COOKIE['auth_cookie'] : '';
    $user_info = check_user_login($auth_cookie);
    
    // 准备回复数据
    $reply_data = array(
        'web_id' => $web_id,
        'parent_id' => $parent_id,
        'content_quality' => 5, // 回复不需要评分
        'service_quality' => 5,
        'trust_level' => 5,
        'comment_content' => $comment_content
    );
    
    if (!empty($user_info)) {
        // 会员回复
        $reply_data['user_id'] = $user_info['user_id'];
        $reply_data['user_email'] = $user_info['user_email'];
        $reply_data['user_name'] = $user_info['nick_name'];
    } else {
        // 匿名回复
        $reply_data['user_id'] = 0;
        $reply_data['user_email'] = '';
        $reply_data['user_name'] = '匿名';
    }
    
    // 提交回复
    $result = submit_website_comment($reply_data);
    output_json($result);
}

/**
 * 输出JSON响应
 */
function output_json($data) {
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}
?>
