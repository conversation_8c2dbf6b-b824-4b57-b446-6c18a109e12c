<!DOCTYPE HTML>
<html>
<head>
<title>{#$site_title#}</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="{#$site_keywords#}" />
<meta name="Description" content="{#$site_description#}" />
<meta name="Copyright" content="Powered By 95dir.com" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
<meta name="robots" content="{#if $robots_content#}{#$robots_content#}{#else#}index,follow{#/if#}" />

<!-- 分页SEO优化 - Canonical链接 -->
<link rel="canonical" href="{#$canonical_url#}" />

<!-- 分页SEO优化 - 上一页/下一页链接 -->
{#if $prev_url#}
<link rel="prev" href="{#$prev_url#}" />
{#/if#}
{#if $next_url#}
<link rel="next" href="{#$next_url#}" />
{#/if#}

<link href="{#$site_root#}themes/default/skin/style.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="{#$site_root#}public/scripts/common.js"></script>
{#include file="script.html"#}

</head>

<body>
{#include file="topbar.html"#}
<div id="wrapper">
	{#include file="header.html"#}
    <div id="mainbox" class="clearfix">
    	<div id="mainbox-left">
        	<div id="subcate" class="clearfix">
            	<h3>{#$current_cate_name#}</h3>
                <ul class="scatelist">
                	{#foreach from=$categories item=sub#}
                    <li><a href="?mod=rejected&cid={#$sub.cate_id#}"{#if $sub.cate_id == $current_cate_id#} class="current"{#/if#}>{#$sub.cate_name#}</a></li>
                    {#/foreach#}
                </ul>
            </div>

            <div class="blank10"></div>
            <div id="listbox" class="clearfix">
            	<h2>{#$current_cate_name#}</h2>
            	<ul class="sitelist"><div class="rejected-notice">
                    <strong>隐私保护说明：</strong>为保护网站隐私，审核不通过期间不显示具体网址。网站管理员可根据反馈进行整改后重新提交。
                </div>
					{#foreach from=$websites item=w name=list#}
                	<li><img src="themes/default/skin/wait.png" width="100" height="80" alt="{#$w.web_name#}" class="thumb" style="filter: hue-rotate(30deg) saturate(1.2);" /><div class="info"><h3><a href="{#$w.web_link#}" title="{#$w.web_name#}">{#$w.web_name#}</a> <span style="background:#f39c12;color:white;padding:2px 6px;border-radius:3px;font-size:10px;">审核不通过</span></h3><p>{#$w.web_intro#}</p><address>
                	    <!-- 隐私保护说明 -->

                <style>
/* 审核不通过页面专用样式 */
.rejected-notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-left: 4px solid #f39c12;
    border-radius: 5px;
    padding: 12px 15px;
    margin-bottom: 15px;
    color: #856404;
    font-size: 13px;
}

/* 确保分页显示在正确位置 */
#listbox .showpage {
    clear: both;
    margin: 20px 0;
    padding: 15px 0;
    text-align: center;
    border-top: 1px solid #eee;
}

/* 分页样式优化 */
#listbox .showpage .total_page {
    margin-right: 10px;
    color: #666;
    font-size: 13px;
}

#listbox .showpage a.pages,
#listbox .showpage span.current {
    display: inline-block;
    padding: 6px 12px;
    margin: 0 2px;
    border: 1px solid #ddd;
    text-decoration: none;
    border-radius: 3px;
    font-size: 13px;
}

#listbox .showpage a.pages:hover {
    background: #f5f5f5;
    border-color: #ccc;
}

#listbox .showpage span.current {
    background: #007cba;
    color: white;
    border-color: #007cba;
}

#listbox .showpage a.next_page,
#listbox .showpage a.last_page {
    margin-left: 5px;
}
</style>
  <!-- 隐私保护：不显示网址 -->
  <span style="color:#f39c12;font-size:12px;">审核不通过</span>
  {#if $w.web_reject_reason#}<br><span style="color:#856404;font-size:11px;">原因：{#$w.web_reject_reason#}</span>{#/if#}
  - {#$w.web_ctime#} -
  <a href="javascript:;" class="addfav"
     onClick="addfav({#$w.web_id#})" title="点击收藏">收藏</a>
</address></div></li>
                	{#foreachelse#}
                	<li>该目录下无任何审核不通过内容！</li>
                	{#/foreach#}
				</ul>
				<div class="clearfix"></div>
            	<div class="showpage">{#$showpage#}</div>
            </div>
        </div>

        <div id="mainbox-right">

            <div id="bestweb" class="mag">
            	<h3>vip站点</h3>
                <ul class="weblist_b">
                   	{#foreach from=get_websites(0, 10, true) item=quick#}
                   	<li><a href="{#$quick.web_link#}"><img src="{#$quick.web_pic#}" width="100" height="80" alt="{#$quick.web_name#}" /></a><strong><a href="{#$quick.web_link#}" title="{#$quick.web_name#}">{#$quick.web_name#}</a></strong><p>{#$quick.web_intro#}</p><address><a href="{#$quick.web_furl#}" target="_blank" class="visit" onClick="clickout({#$quick.web_id#})">{#$quick.web_url#}</a></address></li>
                   	{#/foreach#}
               	</ul>
            </div>

            <div class="blank10"></div>

            <div id="bestweb" class="mag">
            	<h3>推荐站点</h3>
                <ul class="weblist_b">
                   	{#foreach from=get_websites(0, 5, false, true) item=best#}
                   	<li><a href="{#$best.web_link#}"><img src="{#$best.web_pic#}" width="100" height="80" alt="{#$best.web_name#}" /></a><strong><a href="{#$best.web_link#}" title="{#$best.web_name#}">{#$best.web_name#}</a></strong><p>{#$best.web_intro#}</p><address><a href="{#$best.web_furl#}" target="_blank" class="visit" onClick="clickout({#$best.web_id#})">{#$best.web_url#}</a></address></li>
                   	{#/foreach#}
               	</ul>
            </div>
        	<!--<div class="ad250x250">{#get_adcode(7)#}</div>-->
            <div class="blank10"></div>
            <div id="bestart">
            	<h3>推荐资讯</h3>
                <ul class="artlist_b">
                	{#foreach from=get_articles(0, 14) item=art#}
                	<li><a href="{#$art.art_link#}">{#$art.art_title#}</a></li>
                    {#/foreach#}
                </ul>
            </div>
        </div>
    </div>
    {#include file="footer.html"#}
</div>

</body>
</html>
