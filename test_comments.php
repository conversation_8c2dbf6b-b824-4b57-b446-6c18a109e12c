<?php
/**
 * 评论功能测试脚本
 * 用于测试网站评论功能是否正常工作
 */

// 引入必要文件
require_once('./source/init.php');
require_once('./module/website_comments.php');

echo "<h1>网站评论功能测试</h1>";

// 1. 测试数据库表是否存在
echo "<h2>1. 数据库表检查</h2>";
$table_name = $DB->table('website_comments');
$check_sql = "SHOW TABLES LIKE '$table_name'";
$result = $DB->query($check_sql);

if ($DB->num_rows($result) > 0) {
    echo "✓ 评论表 $table_name 存在<br>";
    
    // 检查表结构
    $desc_sql = "DESCRIBE $table_name";
    $desc_result = $DB->query($desc_sql);
    echo "<h3>表结构：</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>";
    while ($row = $DB->fetch_array($desc_result)) {
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$row['Key']}</td>";
        echo "<td>{$row['Default']}</td>";
        echo "<td>{$row['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "✗ 评论表 $table_name 不存在<br>";
    echo "<strong>请先执行 data/sql/add_website_comments_table.sql 创建评论表</strong><br>";
}

// 2. 测试域名过滤功能
echo "<h2>2. 域名过滤功能测试</h2>";
$test_contents = array(
    "这是一个正常的评论内容",
    "这个网站很好用，推荐大家访问 www.example.com",
    "我的网站是 test.cn，欢迎访问",
    "联系我：<EMAIL>",
    "访问 https://www.google.com 搜索更多信息",
    "这里有域名 abc.net 和 xyz.io"
);

foreach ($test_contents as $content) {
    $filtered = filter_domain_names($content);
    $status = ($filtered !== $content) ? "已过滤" : "无需过滤";
    echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd;'>";
    echo "<strong>原文：</strong> $content<br>";
    echo "<strong>过滤后：</strong> $filtered<br>";
    echo "<strong>状态：</strong> <span style='color: " . (($filtered !== $content) ? "red" : "green") . "'>$status</span>";
    echo "</div>";
}

// 3. 测试IP掩码功能
echo "<h2>3. IP地址掩码测试</h2>";
$test_ips = array(
    "*************",
    "********",
    "127.0.0.1",
    "2001:0db8:85a3:0000:0000:8a2e:0370:7334",
    "::1"
);

foreach ($test_ips as $ip) {
    $masked = mask_ip($ip);
    echo "<div style='margin: 5px 0;'>";
    echo "<strong>原IP：</strong> $ip → <strong>掩码后：</strong> $masked";
    echo "</div>";
}

// 4. 测试获取网站列表（用于测试）
echo "<h2>4. 可用于测试的网站列表</h2>";
$websites_sql = "SELECT web_id, web_name, web_url FROM " . $DB->table('websites') . " WHERE web_status = 3 LIMIT 5";
$websites_result = $DB->query($websites_sql);

if ($DB->num_rows($websites_result) > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>网站ID</th><th>网站名称</th><th>网站地址</th><th>测试链接</th></tr>";
    while ($website = $DB->fetch_array($websites_result)) {
        $test_url = "?mod=siteinfo&wid=" . $website['web_id'];
        echo "<tr>";
        echo "<td>{$website['web_id']}</td>";
        echo "<td>{$website['web_name']}</td>";
        echo "<td>{$website['web_url']}</td>";
        echo "<td><a href='$test_url' target='_blank'>测试评论功能</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "没有找到可用于测试的网站";
}

// 5. 测试评论统计功能
echo "<h2>5. 评论统计功能测试</h2>";
// 重新查询第一个网站用于测试
$test_websites_sql = "SELECT web_id, web_name, web_url FROM " . $DB->table('websites') . " WHERE web_status = 3 LIMIT 1";
$test_websites_result = $DB->query($test_websites_sql);

if ($DB->num_rows($test_websites_result) > 0) {
    $test_website = $DB->fetch_array($test_websites_result);
    $web_id = $test_website['web_id'];

    $stats = get_website_comment_stats($web_id);
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px;'>";
    echo "<h3>网站 {$test_website['web_name']} (ID: $web_id) 的评论统计：</h3>";
    echo "<p><strong>总评论数：</strong> {$stats['total_comments']}</p>";
    echo "<p><strong>平均内容质量：</strong> {$stats['avg_content_quality']} 星</p>";
    echo "<p><strong>平均服务质量：</strong> {$stats['avg_service_quality']} 星</p>";
    echo "<p><strong>平均诚信度：</strong> {$stats['avg_trust_level']} 星</p>";
    echo "<p><strong>综合评分：</strong> {$stats['avg_overall']} 星</p>";
    echo "</div>";
}

// 6. 检查必要的函数是否存在
echo "<h2>6. 函数可用性检查</h2>";
$required_functions = array(
    'get_website_comments',
    'get_comment_replies', 
    'submit_website_comment',
    'filter_domain_names',
    'mask_ip',
    'get_website_comment_stats',
    'get_client_ip',
    'check_user_login'
);

foreach ($required_functions as $func) {
    $status = function_exists($func) ? "✓ 存在" : "✗ 不存在";
    $color = function_exists($func) ? "green" : "red";
    echo "<div style='color: $color;'>$status - $func()</div>";
}

// 7. 测试用户登录状态检查
echo "<h2>7. 用户登录状态检查</h2>";
$auth_cookie = isset($_COOKIE['auth_cookie']) ? $_COOKIE['auth_cookie'] : '';
$user_info = check_user_login($auth_cookie);

if (!empty($user_info)) {
    echo "✓ 当前用户已登录<br>";
    echo "<strong>用户信息：</strong><br>";
    echo "用户ID: {$user_info['user_id']}<br>";
    echo "昵称: {$user_info['nick_name']}<br>";
    echo "邮箱: {$user_info['user_email']}<br>";
} else {
    echo "当前用户未登录（将以匿名身份评论）<br>";
}

echo "<h2>测试完成</h2>";
echo "<p>如果所有检查都通过，评论功能应该可以正常使用。</p>";
echo "<p><a href='?mod=siteinfo&wid=1'>点击这里测试评论功能</a>（如果网站ID 1存在）</p>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
}

.success {
    color: green;
}

.error {
    color: red;
}
</style>
