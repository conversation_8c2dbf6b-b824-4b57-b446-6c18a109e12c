<?php
/**
 * 简化的评论功能安装脚本
 */

// 引入必要文件
require_once('./source/init.php');

echo "<h1>网站评论功能安装</h1>";

// 检查评论表是否已存在
$table_name = $DB->table('website_comments');
$check_sql = "SHOW TABLES LIKE '$table_name'";
$result = $DB->query($check_sql);

if ($DB->num_rows($result) > 0) {
    echo "<div style='color: orange; padding: 10px; border: 1px solid orange; border-radius: 4px; margin: 10px 0;'>";
    echo "⚠️ 评论表 $table_name 已存在，无需重复安装。";
    echo "</div>";
} else {
    echo "<div style='color: blue; padding: 10px; border: 1px solid blue; border-radius: 4px; margin: 10px 0;'>";
    echo "📦 开始安装评论功能...";
    echo "</div>";
    
    // 创建评论表的SQL
    $create_table_sql = "
    CREATE TABLE IF NOT EXISTS `$table_name` (
      `comment_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
      `web_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '网站ID',
      `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID，0表示匿名',
      `user_email` varchar(50) NOT NULL DEFAULT '' COMMENT '用户邮箱',
      `user_name` varchar(50) NOT NULL DEFAULT '' COMMENT '用户昵称',
      `user_ip` varchar(45) NOT NULL DEFAULT '' COMMENT '用户IP地址',
      `content_quality` tinyint(1) unsigned NOT NULL DEFAULT '5' COMMENT '内容质量评分1-5星',
      `service_quality` tinyint(1) unsigned NOT NULL DEFAULT '5' COMMENT '网站服务评分1-5星',
      `trust_level` tinyint(1) unsigned NOT NULL DEFAULT '5' COMMENT '网站诚信评分1-5星',
      `comment_content` text NOT NULL COMMENT '评论内容',
      `parent_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父评论ID，0表示主评论',
      `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1=正常，0=隐藏',
      `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
      PRIMARY KEY (`comment_id`),
      KEY `web_id` (`web_id`),
      KEY `user_id` (`user_id`),
      KEY `parent_id` (`parent_id`),
      KEY `create_time` (`create_time`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8;
    ";
    
    try {
        $DB->query($create_table_sql);
        echo "<div style='color: green; padding: 10px; border: 1px solid green; border-radius: 4px; margin: 10px 0;'>";
        echo "✅ 评论表创建成功！";
        echo "</div>";
        
        // 插入一些示例数据（可选）
        if (isset($_GET['demo']) && $_GET['demo'] == '1') {
            echo "<h2>插入示例数据</h2>";
            
            // 获取第一个网站ID用于演示
            $demo_web_sql = "SELECT web_id FROM " . $DB->table('websites') . " WHERE web_status = 3 LIMIT 1";
            $demo_web_result = $DB->query($demo_web_sql);
            
            if ($DB->num_rows($demo_web_result) > 0) {
                $demo_web = $DB->fetch_array($demo_web_result);
                $demo_web_id = $demo_web['web_id'];
                
                $demo_comments = array(
                    array(
                        'web_id' => $demo_web_id,
                        'user_id' => 0,
                        'user_email' => '',
                        'user_name' => '匿名',
                        'user_ip' => '*************',
                        'content_quality' => 5,
                        'service_quality' => 4,
                        'trust_level' => 5,
                        'comment_content' => '这个网站非常好用，界面简洁，功能齐全，强烈推荐！',
                        'parent_id' => 0,
                        'status' => 1,
                        'create_time' => time() - 3600
                    ),
                    array(
                        'web_id' => $demo_web_id,
                        'user_id' => 0,
                        'user_email' => '',
                        'user_name' => '匿名',
                        'user_ip' => '*********',
                        'content_quality' => 4,
                        'service_quality' => 4,
                        'trust_level' => 4,
                        'comment_content' => '网站速度很快，内容也很丰富，就是有些功能还需要完善。',
                        'parent_id' => 0,
                        'status' => 1,
                        'create_time' => time() - 1800
                    )
                );
                
                foreach ($demo_comments as $comment) {
                    $DB->insert($table_name, $comment);
                }
                
                echo "<div style='color: green; padding: 10px; border: 1px solid green; border-radius: 4px; margin: 10px 0;'>";
                echo "✅ 示例评论数据插入成功！";
                echo "</div>";
            }
        }
        
    } catch (Exception $e) {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; border-radius: 4px; margin: 10px 0;'>";
        echo "❌ 创建评论表失败：" . $e->getMessage();
        echo "</div>";
    }
}

// 显示安装结果和下一步操作
echo "<h2>安装完成</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h3>下一步操作：</h3>";
echo "<ol>";
echo "<li><a href='test_comments.php'>运行测试脚本</a> - 检查评论功能是否正常</li>";
echo "<li><a href='?mod=siteinfo&wid=1'>访问网站详情页</a> - 测试评论功能（如果网站ID 1存在）</li>";
echo "<li>如需示例数据，<a href='?demo=1'>点击这里重新安装并添加示例数据</a></li>";
echo "</ol>";
echo "</div>";

echo "<h3>功能特性：</h3>";
echo "<ul>";
echo "<li>✅ 支持匿名评论和会员评论</li>";
echo "<li>✅ 三维度星级评分（内容质量、网站服务、网站诚信）</li>";
echo "<li>✅ 自动过滤域名链接</li>";
echo "<li>✅ IP地址掩码保护隐私</li>";
echo "<li>✅ 评论回复功能</li>";
echo "<li>✅ 防刷评论（5分钟内同IP限制）</li>";
echo "<li>✅ 评分统计和展示</li>";
echo "</ul>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

ul, ol {
    padding-left: 20px;
}

li {
    margin: 5px 0;
}
</style>
