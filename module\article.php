<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$pagename = '文章资讯';
$pageurl = '?mod=article';
$tempfile = 'article.html';
$table = $DB->table('articles');

$pagesize = 10;
$curpage = intval(isset($_GET['page'])? $_GET['page'] : 1);
if ($curpage > 1) {
	$start = ($curpage - 1) * $pagesize;
} else {
	$start = 0;
	$curpage = 1;
}

// 分页SEO优化 - 在标题中添加页码信息
$seo_title_suffix = '';
if ($curpage > 1) {
	$seo_title_suffix = '(第' . $curpage . '页)';
}
		
$cate_id = intval(isset($_GET['cid'])? $_GET['cid'] : (isset($_GET['cate_id'])? $_GET['cate_id'] : 0));
$is_ajax = isset($_GET['ajax']) && $_GET['ajax'] == '1';
$limit = intval(isset($_GET['limit'])? $_GET['limit'] : $pagesize);
$cache_id = $cate_id.'-'.$curpage;

$pageurl .= '&cid='.$cate_id;

if (!$smarty->isCached($tempfile, $cache_id)) {
	$smarty->assign('site_title', $pagename.$seo_title_suffix.' - '.$options['site_name']);
	$smarty->assign('site_keywords', $options['site_keywords']);
	$smarty->assign('site_description', $options['site_description']);
	$smarty->assign('site_path', get_sitepath('article'));
	$smarty->assign('site_rss', get_rssfeed('article'));
	
	$where = "a.art_status=3";
	if ($cate_id > 0) {
		$cate = get_one_category($cate_id);
		if (!$cate) {
			unset($cate);
			redirect('./?mod=index');
		}
		
		$smarty->assign('site_title', $cate['cate_name'].$seo_title_suffix.' - '.$options['site_name']);
		$smarty->assign('site_keywords', !empty($cate['cate_keywords']) ? $cate['cate_keywords'] : $options['site_keywords']);
		$smarty->assign('site_description', !empty($cate['cate_description']) ? $cate['cate_description'] : $options['site_description']);
		$smarty->assign('site_path', get_sitepath($cate['cate_mod'], $cate['cate_id']));
		$smarty->assign('site_rss', get_rssfeed($cate['cate_mod'], $cate['cate_id']));
	
		if ($cate['cate_childcount'] > 0) {
			$where .= " AND a.cate_id IN (".$cate['cate_arrchildid'].")";
			$categories = get_categories($cate['cate_id']);
		} else {
			$where .= " AND a.cate_id=$cate_id";
			$categories = get_categories($cate['root_id']);
		}
	} else {
		$categories = get_categories();
	}
	
	$articles = get_article_list($where, 'ctime', 'DESC', $start, $limit);
	$total = $DB->get_count($table.' a', $where);
	$showpage = showpage($pageurl, $total, $curpage, $pagesize);

	// 如果是AJAX请求，返回JSON数据
	if ($is_ajax) {
		header('Content-Type: application/json; charset=utf-8');

		$response = array(
			'success' => true,
			'articles' => array(),
			'total' => $total
		);

		foreach ($articles as $article) {
			$response['articles'][] = array(
				'art_id' => $article['art_id'],
				'art_title' => htmlspecialchars($article['art_title']),
				'art_link' => $article['art_link'],
				'art_ctime' => $article['art_ctime'],
				'cate_name' => htmlspecialchars($article['cate_name']),
				'is_today' => isset($article['is_today']) ? $article['is_today'] : false
			);
		}

		echo json_encode($response);
		exit;
	}

	$smarty->assign('pagename', $pagename);
	$smarty->assign('cate_id', isset($cate_id) ? $cate_id : 0);
	$smarty->assign('cate_name', isset($cate['cate_name']) ? $cate['cate_name'] : $pagename);
	$smarty->assign('categories', $categories);
	$smarty->assign('total', $total);
	$smarty->assign('articles', $articles);
	$smarty->assign('showpage', $showpage);

	// 分页SEO优化 - 计算分页信息
	$total_pages = ceil($total / $pagesize);
	$prev_page = ($curpage > 1) ? $curpage - 1 : 0;
	$next_page = ($curpage < $total_pages) ? $curpage + 1 : 0;

	// 生成分页相关的URL
	$canonical_url = $options['site_url'] . '?mod=article';
	if ($cate_id > 0) {
		$canonical_url .= '&cid=' . $cate_id;
	}
	if ($curpage > 1) {
		$canonical_url .= '&page=' . $curpage;
	}

	$prev_url = '';
	$next_url = '';
	if ($prev_page > 0) {
		$prev_url = $options['site_url'] . '?mod=article';
		if ($cate_id > 0) {
			$prev_url .= '&cid=' . $cate_id;
		}
		if ($prev_page > 1) {
			$prev_url .= '&page=' . $prev_page;
		}
	}
	if ($next_page > 0) {
		$next_url = $options['site_url'] . '?mod=article';
		if ($cate_id > 0) {
			$next_url .= '&cid=' . $cate_id;
		}
		$next_url .= '&page=' . $next_page;
	}

	// 生成robots meta标签 - 第一页允许索引，其他页面noindex但follow
	$robots_content = ($curpage == 1) ? 'index,follow' : 'noindex,follow';

	// 分页SEO相关变量
	$smarty->assign('curpage', $curpage);
	$smarty->assign('total_pages', $total_pages);
	$smarty->assign('canonical_url', $canonical_url);
	$smarty->assign('prev_url', $prev_url);
	$smarty->assign('next_url', $next_url);
	$smarty->assign('robots_content', $robots_content);

	unset($categories, $articles);
}

smarty_output($tempfile, $cache_id);
?>