<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$pagename = '数据归档';
$pageurl = '?mod=archives';
$tempfile = 'archives.html';
$table = $DB->table('websites');

$pagesize = 12;
$curpage = intval($_GET['page']);
if ($curpage > 1) {
	$start = ($curpage - 1) * $pagesize;
} else {
	$start = 0;
	$curpage = 1;
}

// 分页SEO优化 - 在标题中添加页码信息
$seo_title_suffix = '';
if ($curpage > 1) {
	$seo_title_suffix = '(第' . $curpage . '页)';
}
		
$setdate = intval($_GET['date']);
$is_ajax = isset($_GET['ajax']) && $_GET['ajax'] == '1';
$cache_id = ($setdate && strlen($setdate) == 6 ? $setdate.'-' : '').$curpage;

if (!$smarty->isCached($tempfile, $cache_id)) {
	$smarty->assign('site_title', $pagename.$seo_title_suffix.' - '.$options['site_name']);
	$smarty->assign('site_keywords', '网站存档，目录存档，数据归档');
	$smarty->assign('site_description', '可根据年份、月份来查询，让你及时了解某一时间段内网站的收录情况。');
	$smarty->assign('site_path', get_sitepath().' &raquo; '.$pagename);
	$smarty->assign('site_rss', get_rssfeed());
	
	$where = "w.web_status=3";
	if ($setdate && strlen($setdate) == 6) {
		$year = substr($setdate, 0, 4);
		if ($year >= 2038 || $year <= 1970) {
			$year = gmdate('Y');
			$month = gmdate('m');
		} else {
			$month = substr($setdate, -2);
			$start_timestamp = strtotime($year.'-'.$month.'-1');
			if ($month == 12) {
				$end_year = $year + 1;
				$end_month = 1;
			} else {
				$end_year  = $year;
				$end_month = $month + 1;
			}
			$end_timestamp = strtotime($end_year.'-'.$end_month.'-1');
		}
		$where .= " AND w.web_ctime>='".$start_timestamp."' AND w.web_ctime<'".$end_timestamp."'";
		
		$timetext = $year.'年'.$month.'月';
		
		$smarty->assign('site_title', $timetext.$seo_title_suffix.' - 网站数据归档 - '.$options['site_name']);
		$smarty->assign('site_description', $timetext.'网站数据归档列表。');
		$smarty->assign('site_path', get_sitepath().' &raquo; <a href="'.$pageurl.'">数据归档</a> &raquo; '.$timetext);
		$smarty->assign('timetext', $timetext);
				
		$pageurl .= '&date='.$setdate;
	}
// 处理AJAX请求的limit参数，只对数据归档生效
if ($is_ajax && isset($_GET['limit'])) {
    // 这是首页数据归档的AJAX请求
    $ajax_limit = intval($_GET['limit']);
} else {
    // 普通请求或文章请求，使用默认pagesize
    $ajax_limit = $pagesize;
}
$websites = get_website_list($where, 'web_ctime', 'DESC', $start, $ajax_limit);
	$total = $DB->get_count($table.' w', $where);
	$showpage = showpage($pageurl, $total, $curpage, $pagesize);

	// 如果是AJAX请求，返回JSON数据
	if ($is_ajax) {
		header('Content-Type: application/json; charset=utf-8');

		$response = array(
			'success' => true,
			'websites' => array(),
			'total' => $total
		);

		foreach ($websites as $site) {
			$response['websites'][] = array(
				'web_id' => $site['web_id'],
				'web_name' => htmlspecialchars($site['web_name']),
				'web_url' => htmlspecialchars($site['web_url']),
				'web_link' => $site['web_link'],
				'web_pic' => $site['web_pic'],
				'web_intro' => htmlspecialchars(mb_substr(strip_tags($site['web_intro']), 0, 100, 'UTF-8')),
				'web_ctime' => $site['web_ctime'],
				'is_today' => isset($site['is_today']) ? $site['is_today'] : false
			);
		}

		echo json_encode($response);
		exit;
	}

	$smarty->assign('pagename', $pagename);
	$smarty->assign('archives', get_archives());
	$smarty->assign('total', $total);
	$smarty->assign('websites', $websites);
	$smarty->assign('showpage', $showpage);

	// 分页SEO优化 - 计算分页信息
	$total_pages = ceil($total / $pagesize);
	$prev_page = ($curpage > 1) ? $curpage - 1 : 0;
	$next_page = ($curpage < $total_pages) ? $curpage + 1 : 0;

	// 生成分页相关的URL
	$canonical_url = $options['site_url'] . '?mod=archives';
	if ($setdate && strlen($setdate) == 6) {
		$canonical_url .= '&date=' . $setdate;
	}
	if ($curpage > 1) {
		$canonical_url .= '&page=' . $curpage;
	}

	$prev_url = '';
	$next_url = '';
	if ($prev_page > 0) {
		$prev_url = $options['site_url'] . '?mod=archives';
		if ($setdate && strlen($setdate) == 6) {
			$prev_url .= '&date=' . $setdate;
		}
		if ($prev_page > 1) {
			$prev_url .= '&page=' . $prev_page;
		}
	}
	if ($next_page > 0) {
		$next_url = $options['site_url'] . '?mod=archives';
		if ($setdate && strlen($setdate) == 6) {
			$next_url .= '&date=' . $setdate;
		}
		$next_url .= '&page=' . $next_page;
	}

	// 生成robots meta标签 - 第一页允许索引，其他页面noindex但follow
	$robots_content = ($curpage == 1) ? 'index,follow' : 'noindex,follow';

	// 分页SEO相关变量
	$smarty->assign('curpage', $curpage);
	$smarty->assign('total_pages', $total_pages);
	$smarty->assign('canonical_url', $canonical_url);
	$smarty->assign('prev_url', $prev_url);
	$smarty->assign('next_url', $next_url);
	$smarty->assign('robots_content', $robots_content);

	unset($websites);
}

smarty_output($tempfile, $cache_id);
?>